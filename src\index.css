@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* AgriLift Dark Theme Styles */
@layer components {
  /* Light theme gradients (default) */
  .agrilift-gradient {
    @apply bg-gradient-to-br from-green-50 to-blue-50;
  }

  .agrilift-gradient-primary {
    @apply bg-gradient-to-r from-green-600 to-green-700;
  }

  .agrilift-gradient-secondary {
    @apply bg-gradient-to-r from-green-100 to-green-200;
  }

  .agrilift-card {
    @apply bg-white border border-gray-200 shadow-sm;
  }

  .agrilift-card-header {
    @apply bg-gray-50 border-b border-gray-200;
  }

  .agrilift-text-primary {
    @apply text-gray-900;
  }

  .agrilift-text-secondary {
    @apply text-gray-600;
  }

  .agrilift-text-muted {
    @apply text-gray-500;
  }

  .agrilift-border {
    @apply border-gray-200;
  }

  .agrilift-input {
    @apply bg-white border-gray-300 text-gray-900 placeholder-gray-500;
  }

  .agrilift-button-primary {
    @apply bg-green-600 hover:bg-green-700 text-white;
  }

  .agrilift-button-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900;
  }

  /* Dark theme overrides */
  .dark .agrilift-gradient {
    @apply bg-gradient-to-br from-gray-900 to-gray-800;
  }

  .dark .agrilift-gradient-primary {
    @apply bg-gradient-to-r from-green-700 to-green-800;
  }

  .dark .agrilift-gradient-secondary {
    @apply bg-gradient-to-r from-green-800 to-green-900;
  }

  .dark .agrilift-card {
    @apply bg-gray-800 border-gray-700 shadow-lg;
  }

  .dark .agrilift-card-header {
    @apply bg-gray-700 border-gray-600;
  }

  .dark .agrilift-text-primary {
    @apply text-gray-100;
  }

  .dark .agrilift-text-secondary {
    @apply text-gray-300;
  }

  .dark .agrilift-text-muted {
    @apply text-gray-400;
  }

  .dark .agrilift-border {
    @apply border-gray-700;
  }

  .dark .agrilift-input {
    @apply bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400;
  }

  .dark .agrilift-button-primary {
    @apply bg-green-700 hover:bg-green-600 text-white;
  }

  .dark .agrilift-button-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-gray-100;
  }

  /* Navigation specific dark theme */
  .dark .navbar-bg {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .navbar-text {
    @apply text-gray-200;
  }

  .dark .navbar-text-hover {
    @apply hover:text-green-400;
  }

  .dark .navbar-active {
    @apply text-green-400 bg-gray-700;
  }

  /* Professional Header Styles */
  .professional-header {
    @apply backdrop-blur-2xl border-b border-gray-200/30 shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 50%, rgba(241, 245, 249, 0.98) 100%);
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .professional-header-farmer {
    @apply backdrop-blur-2xl border-b shadow-2xl;
    background: linear-gradient(135deg,
      rgba(76, 175, 80, 0.95) 0%,
      rgba(76, 175, 80, 0.98) 30%,
      rgba(76, 175, 80, 1) 50%,
      rgba(76, 175, 80, 0.98) 70%,
      rgba(76, 175, 80, 0.95) 100%);
    border-bottom: 2px solid rgba(46, 125, 50, 0.3);
    box-shadow:
      0 4px 32px rgba(0, 0, 0, 0.12),
      0 1px 3px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .professional-header-executive {
    @apply backdrop-blur-3xl border-b border-emerald-200/20;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(240, 253, 244, 0.95) 25%,
      rgba(220, 252, 231, 0.95) 50%,
      rgba(187, 247, 208, 0.95) 75%,
      rgba(134, 239, 172, 0.95) 100%);
    box-shadow:
      0 8px 32px rgba(16, 185, 129, 0.08),
      0 4px 16px rgba(16, 185, 129, 0.06),
      0 2px 8px rgba(16, 185, 129, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border-image: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.3), transparent) 1;
  }

  .professional-nav-item {
    @apply relative px-5 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ease-out;
    @apply text-slate-700 bg-white border border-slate-200 backdrop-blur-sm;
    @apply hover:bg-white hover:border-slate-300 hover:shadow-lg hover:text-slate-900;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:bg-white focus:border-blue-300;
    @apply active:shadow-sm;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
    transform: scale(1);
  }

  .professional-nav-item:hover {
    transform: scale(1.02);
  }

  .professional-nav-item:active {
    transform: scale(0.98);
  }

  .professional-nav-item-executive {
    @apply relative px-6 py-3.5 rounded-2xl font-semibold text-sm transition-all duration-500 ease-out;
    @apply text-emerald-700 backdrop-blur-xl border border-emerald-200/50;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(240, 253, 244, 0.8) 50%,
      rgba(220, 252, 231, 0.9) 100%);
    @apply hover:text-emerald-900 hover:border-emerald-300/70;
    @apply focus:outline-none focus:ring-2 focus:ring-emerald-500/40 focus:border-emerald-400/60;
    @apply active:shadow-sm;
    box-shadow:
      0 4px 16px rgba(16, 185, 129, 0.06),
      0 2px 8px rgba(16, 185, 129, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: scale(1);
    position: relative;
    overflow: hidden;
  }

  .professional-nav-item-executive::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
  }

  .professional-nav-item-executive:hover::before {
    left: 100%;
  }

  .professional-nav-item-executive:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow:
      0 8px 32px rgba(16, 185, 129, 0.12),
      0 4px 16px rgba(16, 185, 129, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(240, 253, 244, 0.9) 50%,
      rgba(220, 252, 231, 0.95) 100%);
  }

  .professional-nav-item-executive:active {
    transform: scale(0.98) translateY(0px);
  }

  .professional-nav-item-active {
    @apply relative px-5 py-3 rounded-xl font-bold text-sm transition-all duration-300 ease-out;
    @apply text-white border border-blue-400 backdrop-blur-sm;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3), 0 2px 8px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    @apply hover:shadow-2xl hover:border-blue-300;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-400/40;
    transform: scale(1);
  }

  .professional-nav-item-active:hover {
    transform: scale(1.02);
  }

  .professional-nav-item-active:active {
    transform: scale(0.98);
  }

  .professional-nav-item-active::before {
    content: '';
    @apply absolute inset-0 rounded-xl opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.3) 0%, rgba(59, 130, 246, 0.3) 100%);
  }

  .professional-nav-item-active:hover::before {
    @apply opacity-100;
  }

  .professional-nav-item-active-executive {
    @apply relative px-6 py-3.5 rounded-2xl font-bold text-sm transition-all duration-500 ease-out;
    @apply text-white border border-emerald-400/60 backdrop-blur-xl;
    background: linear-gradient(135deg,
      #10b981 0%,
      #059669 30%,
      #047857 70%,
      #065f46 100%);
    box-shadow:
      0 8px 32px rgba(16, 185, 129, 0.25),
      0 4px 16px rgba(16, 185, 129, 0.15),
      0 2px 8px rgba(16, 185, 129, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    @apply hover:border-emerald-300/80;
    @apply focus:outline-none focus:ring-2 focus:ring-emerald-400/50;
    transform: scale(1);
    position: relative;
    overflow: hidden;
  }

  .professional-nav-item-active-executive::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s ease;
  }

  .professional-nav-item-active-executive:hover::before {
    left: 100%;
  }

  .professional-nav-item-active-executive::after {
    content: '';
    @apply absolute inset-0 rounded-2xl opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg,
      rgba(52, 211, 153, 0.2) 0%,
      rgba(16, 185, 129, 0.2) 50%,
      rgba(5, 150, 105, 0.2) 100%);
  }

  .professional-nav-item-active-executive:hover {
    transform: scale(1.05) translateY(-3px);
    box-shadow:
      0 12px 48px rgba(16, 185, 129, 0.3),
      0 8px 24px rgba(16, 185, 129, 0.2),
      0 4px 12px rgba(16, 185, 129, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.4),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }

  .professional-nav-item-active-executive:hover::after {
    @apply opacity-100;
  }

  .professional-nav-item-active-executive:active {
    transform: scale(0.98) translateY(-1px);
  }

  .professional-logo {
    @apply flex items-center gap-3 font-bold text-xl transition-all duration-300;
    @apply hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500/20 rounded-lg px-2 py-1;
  }

  .professional-logo-executive {
    @apply flex items-center gap-3 font-bold text-xl text-emerald-900 transition-all duration-300;
    @apply hover:scale-105 focus:outline-none focus:ring-2 focus:ring-emerald-500/20 rounded-lg px-2 py-1;
  }

  .professional-button {
    @apply relative px-4 py-2.5 rounded-xl font-semibold text-sm transition-all duration-300 ease-out;
    @apply bg-white border border-slate-200 text-slate-700 backdrop-blur-sm;
    @apply hover:bg-white hover:border-slate-300 hover:text-slate-900;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/25 focus:bg-white focus:border-blue-300;
    @apply active:shadow-sm;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: scale(1);
  }

  .professional-button:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transform: scale(1.02);
  }

  .professional-button:active {
    transform: scale(0.98);
  }

  .professional-button-executive {
    @apply relative px-4 py-2.5 rounded-xl font-semibold text-sm transition-all duration-300 ease-out;
    @apply bg-emerald-50 border border-emerald-200 text-emerald-800 backdrop-blur-sm;
    @apply hover:bg-emerald-100 hover:border-emerald-300 hover:text-emerald-900;
    @apply focus:outline-none focus:ring-2 focus:ring-emerald-500/25 focus:bg-emerald-100 focus:border-emerald-400;
    @apply active:shadow-sm;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.08), 0 1px 3px rgba(16, 185, 129, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transform: scale(1);
  }

  .professional-button-executive:hover {
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.12), 0 2px 6px rgba(16, 185, 129, 0.16), inset 0 1px 0 rgba(255, 255, 255, 0.7);
    transform: scale(1.02);
  }

  .professional-button-executive:active {
    transform: scale(0.98);
  }

  .professional-dropdown {
    @apply bg-white backdrop-blur-2xl border border-slate-200 rounded-2xl p-3;
    @apply animate-in slide-in-from-top-2 duration-300 ease-out;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.06);
  }

  .professional-dropdown-executive {
    @apply bg-emerald-50 backdrop-blur-2xl border border-emerald-200 rounded-2xl p-3;
    @apply animate-in slide-in-from-top-2 duration-300 ease-out;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.15), 0 4px 16px rgba(16, 185, 129, 0.1), 0 1px 4px rgba(16, 185, 129, 0.08);
  }

  .professional-dropdown-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 ease-out;
    @apply text-slate-700 font-medium cursor-pointer;
    @apply hover:bg-slate-100 hover:text-slate-900 hover:shadow-sm;
    @apply focus:outline-none focus:bg-slate-100 focus:ring-2 focus:ring-blue-500/20;
    transform: scale(1);
  }

  .professional-dropdown-item:hover {
    transform: scale(1.01);
  }

  .professional-dropdown-item:active {
    transform: scale(0.99);
  }

  .professional-dropdown-item-executive {
    @apply flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 ease-out;
    @apply text-emerald-700 font-medium cursor-pointer;
    @apply hover:bg-emerald-100 hover:text-emerald-900 hover:shadow-sm;
    @apply focus:outline-none focus:bg-emerald-100 focus:ring-2 focus:ring-emerald-500/20;
    transform: scale(1);
  }

  .professional-dropdown-item-executive:hover {
    transform: scale(1.01);
  }

  .professional-dropdown-item-executive:active {
    transform: scale(0.99);
  }

  .professional-notification-badge {
    @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5;
    @apply animate-pulse shadow-lg;
  }

  .professional-mobile-menu {
    @apply bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-2xl rounded-xl;
    @apply animate-in slide-in-from-top-4 duration-300;
  }

  .professional-mobile-menu-farmer {
    @apply backdrop-blur-xl shadow-2xl rounded-xl;
    @apply animate-in slide-in-from-top-4 duration-300;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 252, 0.95) 50%,
      rgba(240, 253, 244, 0.95) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
  }

  .professional-mobile-menu-executive {
    @apply bg-emerald-50/95 backdrop-blur-xl border border-emerald-200/50 shadow-2xl rounded-xl;
    @apply animate-in slide-in-from-top-4 duration-300;
  }

  /* Dark theme overrides for professional headers */
  .dark .professional-header {
    @apply bg-gray-900/95 border-gray-700/50;
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);
  }

  .dark .professional-header-farmer {
    @apply backdrop-blur-2xl border-b shadow-2xl;
    background: linear-gradient(135deg,
      rgba(46, 125, 50, 0.9) 0%,
      rgba(46, 125, 50, 0.95) 30%,
      rgba(46, 125, 50, 1) 50%,
      rgba(46, 125, 50, 0.95) 70%,
      rgba(46, 125, 50, 0.9) 100%);
    border-bottom: 2px solid rgba(76, 175, 80, 0.4);
    box-shadow:
      0 4px 32px rgba(0, 0, 0, 0.3),
      0 1px 3px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .dark .professional-header-executive {
    @apply bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-emerald-900/95 border-emerald-700/50;
  }

  .dark .professional-nav-item {
    @apply text-gray-300 hover:bg-gray-800/80 hover:text-white;
  }

  .dark .professional-nav-item-executive {
    @apply text-emerald-300 hover:bg-emerald-800/80 hover:text-emerald-100;
  }

  /* Enhanced Farmer Portal Navigation Items */
  .professional-nav-item-farmer {
    @apply text-white hover:bg-white/20 hover:text-white transition-all duration-200;
    @apply border border-transparent hover:border-white/30 rounded-lg px-3 py-2;
    @apply font-medium;
  }

  .dark .professional-nav-item-farmer {
    @apply text-white hover:bg-white/10 hover:text-white;
    @apply border border-transparent hover:border-white/20;
  }

  /* Farmer Portal Logo and Branding */
  .farmer-portal-logo {
    @apply text-white hover:text-white/90 transition-colors duration-200;
    @apply drop-shadow-lg;
  }

  .dark .farmer-portal-logo {
    @apply text-white hover:text-white/90;
  }

  /* Farmer Portal Button Styles */
  .farmer-portal-button {
    @apply bg-white/90 hover:bg-white text-gray-800 hover:text-gray-900;
    @apply border border-white/50 hover:border-white transition-all duration-200;
    @apply shadow-md hover:shadow-lg;
  }

  .dark .farmer-portal-button {
    @apply bg-white/10 hover:bg-white/20 text-white hover:text-white;
    @apply border border-white/20 hover:border-white/40;
  }

  .dark .professional-button {
    @apply bg-gray-800/80 border-gray-700/80 text-gray-300 hover:bg-gray-800 hover:border-gray-600;
  }

  .dark .professional-button-executive {
    @apply bg-emerald-800/80 border-emerald-700/80 text-emerald-300 hover:bg-emerald-800 hover:border-emerald-600;
  }

  .dark .professional-dropdown {
    @apply bg-gray-900/95 border-gray-700/50;
  }

  .dark .professional-dropdown-executive {
    @apply bg-gray-900/95 border-emerald-700/50;
  }

  .dark .professional-dropdown-item {
    @apply text-gray-300 hover:bg-gray-800/80 hover:text-white;
  }

  .dark .professional-dropdown-item-executive {
    @apply text-emerald-300 hover:bg-emerald-800/80 hover:text-emerald-100;
  }

  /* Modern Executive Dashboard Cards */
  .executive-kpi-card {
    @apply relative overflow-hidden rounded-3xl border border-white/20 backdrop-blur-xl transition-all duration-500 ease-out;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 252, 0.9) 50%,
      rgba(241, 245, 249, 0.95) 100%);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.04),
      0 4px 16px rgba(0, 0, 0, 0.02),
      0 2px 8px rgba(0, 0, 0, 0.01),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .executive-kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #059669, #047857);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .executive-kpi-card:hover::before {
    opacity: 1;
  }

  .executive-kpi-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 64px rgba(0, 0, 0, 0.08),
      0 12px 32px rgba(0, 0, 0, 0.04),
      0 6px 16px rgba(0, 0, 0, 0.02),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(16, 185, 129, 0.2);
  }

  .executive-chart-card {
    @apply relative overflow-hidden rounded-3xl border border-gray-200/50 backdrop-blur-xl transition-all duration-500 ease-out;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.98) 0%,
      rgba(249, 250, 251, 0.95) 50%,
      rgba(243, 244, 246, 0.98) 100%);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.06),
      0 6px 24px rgba(0, 0, 0, 0.03),
      0 3px 12px rgba(0, 0, 0, 0.02),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  .executive-chart-card:hover {
    transform: translateY(-4px);
    box-shadow:
      0 16px 64px rgba(0, 0, 0, 0.08),
      0 8px 32px rgba(0, 0, 0, 0.04),
      0 4px 16px rgba(0, 0, 0, 0.02),
      inset 0 1px 0 rgba(255, 255, 255, 0.95);
    border-color: rgba(16, 185, 129, 0.15);
  }

  /* Professional animations and effects */
  .professional-glow {
    @apply relative;
  }

  .professional-glow::before {
    content: '';
    @apply absolute inset-0 rounded-lg bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-blue-400/20;
    @apply opacity-0 transition-opacity duration-500 blur-sm;
  }

  .professional-glow:hover::before {
    @apply opacity-100;
  }

  .professional-glow-farmer::before {
    @apply bg-gradient-to-r from-green-400/20 via-emerald-400/20 to-green-400/20;
  }

  .professional-glow-executive::before {
    @apply bg-gradient-to-r from-emerald-400/20 via-green-400/20 to-emerald-400/20;
  }

  .professional-shimmer {
    @apply relative overflow-hidden;
  }

  .professional-shimmer::after {
    content: '';
    @apply absolute top-0 left-0 w-full h-full;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease-in-out;
  }

  .professional-shimmer:hover::after {
    transform: translateX(100%);
  }

  .professional-pulse {
    @apply animate-pulse;
    animation-duration: 2s;
  }

  .professional-bounce {
    @apply hover:animate-bounce;
    animation-duration: 1s;
  }

  /* Professional typography */
  .professional-text-gradient {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent;
  }

  .professional-text-gradient-farmer {
    @apply bg-gradient-to-r from-green-600 via-emerald-600 to-green-600 bg-clip-text text-transparent;
  }

  .professional-text-gradient-executive {
    @apply bg-gradient-to-r from-emerald-600 via-green-600 to-emerald-600 bg-clip-text text-transparent;
  }

  /* Professional shadows */
  .professional-shadow-soft {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .professional-shadow-medium {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .professional-shadow-large {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .professional-shadow-farmer {
    box-shadow: 0 4px 6px -1px rgba(34, 197, 94, 0.1), 0 2px 4px -1px rgba(34, 197, 94, 0.06);
  }

  .professional-shadow-executive {
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.1), 0 2px 4px -1px rgba(16, 185, 129, 0.06);
  }

  /* Professional Layout and Alignment */
  .professional-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .professional-nav-container {
    @apply flex items-center justify-between h-16 lg:h-20;
  }

  .professional-nav-left {
    @apply flex items-center gap-4 lg:gap-6;
  }

  .professional-nav-center {
    @apply hidden md:flex items-center justify-center flex-1 max-w-2xl mx-8;
  }

  .professional-nav-right {
    @apply flex items-center gap-3 lg:gap-4;
  }

  .professional-nav-items {
    @apply flex items-center gap-2 lg:gap-3;
  }

  .professional-mobile-trigger {
    @apply md:hidden;
  }

  .professional-logo-container {
    @apply flex items-center gap-3;
  }

  /* Professional Button Variants */
  .professional-button-primary {
    @apply relative px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ease-out;
    @apply text-white border border-blue-500/50 backdrop-blur-sm;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25), 0 2px 8px rgba(59, 130, 246, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    @apply hover:shadow-2xl hover:scale-[1.02] hover:border-blue-400/60;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-400/40;
    @apply active:scale-[0.98];
  }

  .professional-button-primary-executive {
    @apply relative px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ease-out;
    @apply text-white border border-emerald-500/50 backdrop-blur-sm;
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.25), 0 2px 8px rgba(16, 185, 129, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    @apply hover:shadow-2xl hover:scale-[1.02] hover:border-emerald-400/60;
    @apply focus:outline-none focus:ring-2 focus:ring-emerald-400/40;
    @apply active:scale-[0.98];
  }

  .professional-button-secondary {
    @apply relative px-5 py-2.5 rounded-xl font-medium text-sm transition-all duration-300 ease-out;
    @apply bg-slate-100 border border-slate-200 text-slate-700 backdrop-blur-sm;
    @apply hover:bg-slate-200 hover:border-slate-300 hover:text-slate-900;
    @apply focus:outline-none focus:ring-2 focus:ring-slate-400/25 focus:bg-slate-200 focus:border-slate-300;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.08);
    transform: scale(1);
  }

  .professional-button-secondary:hover {
    transform: scale(1.01);
  }

  .professional-button-secondary:active {
    transform: scale(0.99);
  }

  .professional-button-ghost {
    @apply relative px-4 py-2.5 rounded-xl font-medium text-sm transition-all duration-300 ease-out;
    @apply text-slate-600 hover:text-slate-900 hover:bg-slate-100;
    @apply focus:outline-none focus:ring-2 focus:ring-slate-400/20 focus:bg-slate-100;
    transform: scale(1);
  }

  .professional-button-ghost:hover {
    transform: scale(1.01);
  }

  .professional-button-ghost:active {
    transform: scale(0.99);
  }

  /* Market and product cards dark theme */
  .dark .product-card {
    @apply bg-gray-800 border-gray-700 hover:bg-gray-700;
  }

  .dark .product-card-hover {
    @apply hover:shadow-xl hover:border-green-600;
  }

  /* Dashboard specific dark theme */
  .dark .dashboard-bg {
    @apply bg-gray-900;
  }

  .dark .dashboard-card {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .dashboard-stat-card {
    @apply bg-gradient-to-r from-gray-800 to-gray-700 border-gray-600;
  }

  /* Form elements dark theme */
  .dark .form-input {
    @apply bg-gray-700 border-gray-600 text-gray-100 focus:border-green-500;
  }

  .dark .form-label {
    @apply text-gray-300;
  }

  .dark .form-select {
    @apply bg-gray-700 border-gray-600 text-gray-100;
  }

  /* Badge and status indicators dark theme */
  .dark .badge-success {
    @apply bg-green-800 text-green-200;
  }

  .dark .badge-warning {
    @apply bg-yellow-800 text-yellow-200;
  }

  .dark .badge-error {
    @apply bg-red-800 text-red-200;
  }

  .dark .badge-info {
    @apply bg-blue-800 text-blue-200;
  }
}

/* Navigation spacing utilities */
@layer utilities {
  .nav-spacing {
    @apply pt-20 lg:pt-24;
  }

  .nav-spacing-sm {
    @apply pt-16 lg:pt-20;
  }
}

/* Weather icon animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(251, 191, 36, 0.5), 0 0 10px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.8), 0 0 20px rgba(251, 191, 36, 0.5);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes pulse-soft {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fade-in-out {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 3s ease-in-out infinite;
}

.animate-rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

.animate-fade-in-out {
  animation: fade-in-out 2s ease-in-out infinite;
}

/* Enhanced animations for machinery page */
@keyframes fade-in {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slide-in-left {
  from { 
    opacity: 0; 
    transform: translateX(-30px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

@keyframes slide-in-right {
  from { 
    opacity: 0; 
    transform: translateX(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

@keyframes scale-in {
  from { 
    opacity: 0; 
    transform: scale(0.9); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

@keyframes hover-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.5s ease-out forwards;
}

/* Enhanced Executive Dashboard Animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(16, 185, 129, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-hover-bounce:hover {
  animation: hover-bounce 0.6s ease-in-out;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Improved text clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
