{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:backend dev:frontend", "dev:smart": "node start.cjs", "dev:frontend": "vite --port 8080", "dev:backend": "cd backend && npm run dev", "dev:frontend-only": "vite --port 8080", "start": "npm-run-all --parallel start:backend start:frontend", "start:frontend": "vite preview", "start:backend": "cd backend && npm start", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:staging": "cross-env NODE_ENV=staging jest --config=jest.staging.config.js", "docker:build": "docker build -t agri-lift-frontend .", "docker:run": "docker run -p 8080:8080 agri-lift-frontend", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f", "deploy:staging": "npm run build && aws s3 sync dist/ s3://agri-lift-staging --delete", "deploy:production": "npm run build && aws s3 sync dist/ s3://agri-lift-production --delete"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^9.4.2", "@react-three/fiber": "^8.13.7", "@tanstack/react-query": "^5.56.2", "@types/leaflet": "^1.9.18", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.16.0", "input-otp": "^1.2.4", "leaflet": "^1.9.4", "lucide-react": "^0.462.0", "mongoose": "^8.16.1", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-leaflet": "^4.2.1", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.1", "three": "^0.152.2", "vaul": "^0.9.3", "vite_react_shadcn_ts": "file:", "zod": "^3.23.8", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "jest-transform-stub": "^2.0.0", "lovable-tagger": "^1.1.7", "npm-run-all": "^4.1.5", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-jest": "^29.4.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}